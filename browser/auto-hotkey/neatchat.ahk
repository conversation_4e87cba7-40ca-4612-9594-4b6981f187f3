#Requires Autohotkey v2

; win+R 执行shell:startup，然后把这个脚本拖进去，就能开机自启

exePath := "D:\chatgpt-browser\myBrowser.exe"
threshold := 400
maxPressDuration := 300  ; 最大按键持续时间（毫秒），超过此时间视为长按

lastLUp := 0
LCtrlLastDown := 0

; 记录 down 状态，忽略没有对应 down 的 up（常见于 RDP 合成 up）
LCtrlIsDown := false

; 捕获 Left Control down（~ 保留系统原有行为）
~LControl:: {
    global LCtrlIsDown, LCtrlLastDown
    if (not LCtrlIsDown) { ; 第一次按下才记录
       ; 记录按下时间
       LCtrlLastDown := A_TickCount
    }
    LCtrlIsDown := true

    ; 如果需要调试解除下面注释
    ; Tooltip(LCtrlLastDown)
    return
}

~LControl up:: {
    global lastLUp, threshold, exePath, LCtrlIsDown, LCtrlLastDown, maxPressDuration
    if (LCtrlIsDown) {
        curr := A_TickCount
        pressDuration := curr - LCtrlLastDown
        ; Tooltip(pressDuration)

        ; 检查是否为短按
        if (pressDuration <= maxPressDuration) {
            if (curr - lastLUp < threshold) {
                ; 检查没有其它修饰键按下（避免在组合键使用时触发）
                if !(GetKeyState("LShift","P") || GetKeyState("RShift","P")
                    || GetKeyState("LAlt","P") || GetKeyState("RAlt","P")
                    || GetKeyState("LWin","P") || GetKeyState("RWin","P")
                    || GetKeyState("RControl","P")) {

                    ; 检查是否有其他普通键被按下（避免组合键如Ctrl+V触发）
                    hasOtherKeyPressed := false
                    ; 检查常用的字母数字键
                    Loop 26 {
                        key := Chr(64 + A_Index)  ; A-Z
                        if (GetKeyState(key, "P")) {
                            hasOtherKeyPressed := true
                            break
                        }
                    }
                    ; 检查数字键
                    if (!hasOtherKeyPressed) {
                        Loop 10 {
                            key := Chr(47 + A_Index)  ; 0-9
                            if (GetKeyState(key, "P")) {
                                hasOtherKeyPressed := true
                                break
                            }
                        }
                    }
                    ; 检查一些常用的特殊键
                    if (!hasOtherKeyPressed) {
                        specialKeys := ["Space", "Enter", "Tab", "Backspace", "Delete", "Insert", "Home", "End", "PgUp", "PgDn", "Up", "Down", "Left", "Right", "F1", "F2", "F3", "F4", "F5", "F6", "F7", "F8", "F9", "F10", "F11", "F12"]
                        for key in specialKeys {
                            if (GetKeyState(key, "P")) {
                                hasOtherKeyPressed := true
                                break
                            }
                        }
                    }

                    ; 只有在没有其他键被按下时才触发
                    if (!hasOtherKeyPressed) {
                        exeDir := RegExReplace(exePath, "\\[^\\]+$")
                        if (StrLen(exeDir) == 0) {
                            exeDir := A_ScriptDir
                        }
                        Run(exePath, exeDir)
                        lastLUp := 0  ; 重置，避免连续触发
                        LCtrlIsDown := false
                        LCtrlLastDown := 0
                        return
                    }
                }
            }
            ; 更新最后一次短按的时间
            lastLUp := curr
        }
	} else {
        ; 没有对应 down，可能是 RDP 合成的 up —— 忽略
        ; Tooltip("忽略合成 LControl up")
    }
    ; 重置状态
    LCtrlIsDown := false
    LCtrlLastDown := 0
    return
}
